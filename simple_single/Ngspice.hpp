/**********************************************************************
 *
 * FaradayEDA 
 * http://www.faradynamics.com
 *
 * Copyright (C) 2017-2019 Faraday Dynamics, Ltd.
 * All rights reserved.
 *
 * Author: <EMAIL>
 *
 * Version V201904
 * Date: 2019-03-07 11:16:12
 *
 **********************************************************************/

#ifndef FARADAY_NGSPICE_HPP
#define FARADAY_NGSPICE_HPP

#include <complex>
#include <memory>
#include <string>
#include <vector>
#include <map>

namespace Faraday {

class Ngspice {
  public:
    static std::shared_ptr<Ngspice> instance();
    
    // Execute ngspice command
    bool command(const std::string& cmd) const;
    void init() const;
    
    // Load netlist into ngspice
    // Netlist is divided into lines
    int load_netlist(const std::vector<std::string>&) const;
    
    // Load circuit from file
    bool load_circuit_file(const std::string& filename) const;
    
    bool run() const;
    
    // Get complex vectors (for AC analysis)
    std::vector<std::complex<double>> ivec(const std::string& label) const;
    std::vector<std::complex<double>> vvec(const std::string &label) const;
    
    // Get real vectors (for DC/transient analysis)
    std::vector<double> ivec_dc(const std::string &label, const std::string &type = "current") const;
    std::map<std::string, double> ivec_dc_current(const std::string &device_name) const;
    std::vector<double> tran_vvec(const std::string &label) const;
    std::vector<double> tran_ivec(const std::string &label) const;
    
    // Destructor
    ~Ngspice();

  private:
    // Private constructor for singleton pattern
    Ngspice();
    
    // Disable copy constructor and assignment operator
    Ngspice(const Ngspice&) = delete;
    Ngspice& operator=(const Ngspice&) = delete;
    
    // Callback functions
    static int cbSendChar(char*, int, void*);
    static int cbSendStat(char*, int, void*);
    static int cbControlledExit(int, bool, bool, int, void*);
    static int cbBGThreadRunning(bool, int, void*);
};

} // namespace Faraday

#endif
