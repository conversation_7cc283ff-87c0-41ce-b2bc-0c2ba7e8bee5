/*
 * Simple Single Thread NGSpice Test using Ngspice C++ wrapper
 * Demonstrates the usage of the Ngspice class to encapsulate shared library functionality
 */

#include <iostream>
#include <vector>
#include <string>
#include <exception>
#include "Ngspice.hpp"

using namespace std;
using namespace Faraday;

int main()
{
    cout << "========================================" << endl;
    cout << "Simple Single Thread NGSpice Test (C++)" << endl;
    cout << "Using Ngspice C++ Wrapper Class" << endl;
    cout << "========================================" << endl;
    
    try {
        // Get Ngspice instance (singleton pattern)
        cout << "Getting NGSpice instance..." << endl;
        auto ngspice = Ngspice::instance();
        
        cout << "NGSpice initialized successfully" << endl;
        cout << "========================================" << endl;
        
        // Initialize ngspice (reset any previous state)
        cout << "Initializing NGSpice state..." << endl;
        ngspice->init();
        
        // Load circuit file
        const string circuit_file = "../TransmissionLines/cpl_ibm1.sp";
        cout << "Loading circuit: " << circuit_file << endl;
        cout << "========================================" << endl;
        
        bool load_success = ngspice->load_circuit_file(circuit_file);
        if (!load_success) {
            cerr << "ERROR: Failed to load circuit file" << endl;
            return 1;
        }
        
        cout << "Circuit loaded successfully" << endl;
        cout << "========================================" << endl;
        cout << "Starting simulation..." << endl;
        cout << "========================================" << endl;
        
        // Run simulation
        bool run_success = ngspice->run();
        if (!run_success) {
            cerr << "ERROR: Failed to run simulation" << endl;
            return 1;
        }
        
        cout << "========================================" << endl;
        cout << "Simulation completed successfully!" << endl;
        cout << "========================================" << endl;
        
        
    } catch (const std::exception& e) {
        cerr << "ERROR: " << e.what() << endl;
        return 1;
    }
    
    return 0;
}
