# NGSpice Simple Single Thread 示例

这个目录包含了使用NGSpice shared library的简单示例，包括原始的C版本和改进的C++封装版本。

## 文件说明

### 核心文件
- `Ngspice.hpp` - Ngspice C++封装类的头文件
- `Ngspice.cpp` - Ngspice C++封装类的实现
- `main.c` - 原始的C语言版本示例
- `main.cpp` - 使用Ngspice类的C++版本示例
- `example_netlist.cpp` - 演示如何使用netlist字符串的示例
- `test_circuit.cir` - 测试电路文件
- `CMakeLists.txt` - CMake构建配置

### 改进内容

#### Ngspice类的改进：
1. **修复了命名空间拼写错误**：`Farady` → `Faraday`
2. **移除了未使用的方法**：删除了`instance1()`方法
3. **改进了单例模式**：
   - 构造函数设为私有
   - 禁用拷贝构造函数和赋值操作符
   - 添加了析构函数
4. **修复了内存泄漏**：在`load_netlist`方法中正确释放`strdup`分配的内存
5. **改进了错误处理**：
   - 构造函数中检查ngSpice_Init的返回值
   - command方法返回实际的成功/失败状态
6. **添加了新功能**：
   - `load_circuit_file()` - 直接从文件加载电路
7. **清理了调试代码**：移除了混乱的NDEBUG宏使用

## 构建和运行

### 构建
```bash
cd simple_single
mkdir -p build
cd build
cmake ..
make
```

### 运行示例

1. **C语言版本**：
```bash
./ngspice_simple_single_c
```

2. **C++版本（从文件加载）**：
```bash
./ngspice_simple_single
```

3. **C++版本（使用netlist字符串）**：
```bash
./ngspice_netlist_example
```

## 使用示例

### 基本用法
```cpp
#include "Ngspice.hpp"
using namespace Faraday;

// 获取单例实例
auto ngspice = Ngspice::instance();

// 初始化
ngspice->init();

// 从文件加载电路
ngspice->load_circuit_file("circuit.cir");

// 或者从netlist字符串加载
std::vector<std::string> netlist = {
    "Simple Circuit",
    "V1 1 0 DC 5",
    "R1 1 0 1k",
    ".op",
    ".end"
};
ngspice->load_netlist(netlist);

// 运行仿真
ngspice->run();

// 读取结果
auto voltage = ngspice->ivec_dc("1", "voltage");
auto current = ngspice->ivec_dc("v1", "current");
```

### 支持的分析类型
- **DC分析**：使用`ivec_dc()`获取电压/电流
- **瞬态分析**：使用`tran_vvec()`和`tran_ivec()`
- **AC分析**：使用`vvec()`和`ivec()`获取复数结果
- **器件电流**：使用`ivec_dc_current()`获取器件内部电流

## 测试电路

包含的`test_circuit.cir`是一个简单的电阻分压器：
- 输入电压：5V
- R1, R2：各1kΩ
- 输出电压应该是2.5V

## 依赖要求

- NGSpice库（带shared library支持）
- CMake 3.12+
- C++17编译器
- C99编译器（用于C版本）

## 注意事项

1. 确保NGSpice安装时启用了shared library支持
2. Ngspice类使用单例模式，在整个程序生命周期中只有一个实例
3. 所有方法都是线程安全的（基于NGSpice shared library的线程安全性）
4. 记得在使用前调用`init()`方法来重置NGSpice状态
