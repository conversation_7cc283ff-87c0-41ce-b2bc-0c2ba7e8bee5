# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ngspice_parallel/simple_single

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ngspice_parallel/simple_single/build

# Include any dependencies generated for this target.
include CMakeFiles/ngspice_netlist_example.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ngspice_netlist_example.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ngspice_netlist_example.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ngspice_netlist_example.dir/flags.make

CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o: CMakeFiles/ngspice_netlist_example.dir/flags.make
CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o: ../example_netlist.cpp
CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o: CMakeFiles/ngspice_netlist_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o -MF CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o.d -o CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o -c /home/<USER>/ngspice_parallel/simple_single/example_netlist.cpp

CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngspice_parallel/simple_single/example_netlist.cpp > CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.i

CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngspice_parallel/simple_single/example_netlist.cpp -o CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.s

CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o: CMakeFiles/ngspice_netlist_example.dir/flags.make
CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o: ../Ngspice.cpp
CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o: CMakeFiles/ngspice_netlist_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o -MF CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o.d -o CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o -c /home/<USER>/ngspice_parallel/simple_single/Ngspice.cpp

CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ngspice_parallel/simple_single/Ngspice.cpp > CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.i

CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ngspice_parallel/simple_single/Ngspice.cpp -o CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.s

# Object files for target ngspice_netlist_example
ngspice_netlist_example_OBJECTS = \
"CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o" \
"CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o"

# External object files for target ngspice_netlist_example
ngspice_netlist_example_EXTERNAL_OBJECTS =

ngspice_netlist_example: CMakeFiles/ngspice_netlist_example.dir/example_netlist.cpp.o
ngspice_netlist_example: CMakeFiles/ngspice_netlist_example.dir/Ngspice.cpp.o
ngspice_netlist_example: CMakeFiles/ngspice_netlist_example.dir/build.make
ngspice_netlist_example: /usr/local/lib/libngspice.so
ngspice_netlist_example: CMakeFiles/ngspice_netlist_example.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable ngspice_netlist_example"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ngspice_netlist_example.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ngspice_netlist_example.dir/build: ngspice_netlist_example
.PHONY : CMakeFiles/ngspice_netlist_example.dir/build

CMakeFiles/ngspice_netlist_example.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ngspice_netlist_example.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ngspice_netlist_example.dir/clean

CMakeFiles/ngspice_netlist_example.dir/depend:
	cd /home/<USER>/ngspice_parallel/simple_single/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ngspice_parallel/simple_single /home/<USER>/ngspice_parallel/simple_single /home/<USER>/ngspice_parallel/simple_single/build /home/<USER>/ngspice_parallel/simple_single/build /home/<USER>/ngspice_parallel/simple_single/build/CMakeFiles/ngspice_netlist_example.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/ngspice_netlist_example.dir/depend

